{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مجلس قروي كفرعين - خدماتي</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute("data-theme", "light");
      localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="{% static 'css/index.css' %}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!--Link the js file-->
    <script src="{% static 'js/services.js' %}" defer></script>
    <!-- Theme JS -->
    <script src="{% static 'js/theme.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <!--Link the footer js file-->
    <script src="{% static 'js/footer.js' %}" defer></script>

    <!-- Font Awesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Professional Services CSS -->
    <link href="{% static 'css/professional-services.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
  </head>
  <body id="page-top">
    <!-- Professional Navigation Bar -->
    <nav
      class="navbar navbar-expand-lg navbar-dark fixed-top shadow-lg"
      id="mainNav"
    >
      <div class="container px-4">
        <!-- Logo and title -->
        <a
          class="logo d-flex align-items-center text-decoration-none"
          href="/services"
          title="صفحة خدماتي"
        >
          <img
            src="{% static 'images/logo.png' %}"
            alt="شعار الموقع"
            width="150"
            height="100"
            class="d-inline-block"
          />
          <span class="ms-2 d-none d-lg-inline text-light fw-bold text-shadow">خدماتي</span>
        </a>
        <button
          class="navbar-toggler border-0 shadow-sm"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarResponsive"
          aria-controls="navbarResponsive"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarResponsive">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#complaints-suggestions">
                <i class="fas fa-comment-alt me-1"></i> شكاوى واقتراحات
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#contact-us">
                <i class="fas fa-envelope me-1"></i> تواصل معنا
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link px-3 mx-1 rounded-pill"
                href="{% url 'index' %}"
              >
                <i class="fas fa-sign-out-alt me-1"></i>
                <span>خروج</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
    <!-- End of Navbar -->
    <!-- check the suggestions and complaints form status-->
    {% if suggestions_status and request.method == "POST" %}
    <div class="alert text-center mt-1 mb-0" role="alert" id="alertMsg">
      {% if suggestions_status == "success" %}
      <script>
        const delay = 5000; // 5 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-success text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
              <strong>نجاح:</strong> تم إرسال الطلب بنجاح!
            `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 5 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% elif suggestions_status == "conflict suggestions complaints" %}
      <script>
        const delay = 8000; // 8 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-warning text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
              <strong>تحذير:</strong> لم يتم إرسال الطلب، لقد تجاوزت الحد الأقصى من الشكاوى أو الإقتراحات، للمزيد يمكنك مراجعة المجلس.
            `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 8 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% else %}
      <script>
        const delay = 5000; // 5 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-danger text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
            <strong>خطأ:</strong> حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.
          `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 5 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% endif %}
    </div>
    {% endif %}
    <!-- Services Section -->
    <section id="services-section" class="container">
      <div class="services-container">
        <!-- User Reservations and Transactions Section -->
      <div class="row justify-content-center mb-4">
        <div class="col-12">
          <div class="card mb-3 shadow border-0">
            <div class="card-body bg-light rounded">
              <h4 class="card-title text-center mb-4 text-dark">حجوزات القاعة والمعاملات الخاصة بك</h4>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <h5 class="text-primary mb-2"><i class="fa fa-calendar-check me-2"></i>حجوزات القاعة</h5>
                  {% if user_reservations %}
                    <ul class="list-group">
                      {% for res in user_reservations %}
                        <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
                          <div>
                            <span class="fw-bold">من:</span> {{ res.start_date|default:'-' }}<br>
                            <span class="fw-bold">إلى:</span> {{ res.end_date|default:'-' }}<br>
                            <span class="fw-bold">نوع المناسبة:</span> {{ res.event_type|default:'-' }}
                            {% if res.status %}<br><span class="badge bg-info text-dark">{{ res.status }}</span>{% endif %}
                          </div>
                          <div>
                            {% if res.status == 'pending' %}
                              <button class="btn btn-secondary btn-sm" disabled>بانتظار موافقة الإدارة</button>
                            {% else %}
                              <form method="post" action="{% url 'request_cancel_reservation' res.doc_id %}" style="display:inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-outline-danger btn-sm">طلب إلغاء</button>
                              </form>
                            {% endif %}
                          </div>
                        </li>
                      {% endfor %}
                    </ul>
                  {% else %}
                    <div class="text-muted">لا يوجد حجوزات حالية.</div>
                  {% endif %}
                </div>
                <div class="col-md-6 mb-3">
                  <h5 class="text-success mb-2"><i class="fa fa-file-alt me-2"></i>المعاملات</h5>
                  {% if user_transactions %}
                    <ul class="list-group">
                      {% for txn in user_transactions %}
                        <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
                          <div>
                            <span class="fw-bold">نوع المعاملة:</span> {{ txn.transaction_type|default:'-' }}<br>
                            <span class="fw-bold">تاريخ الطلب:</span> {{ txn.created_at|default:'-' }}
                            {% if txn.status %}<br><span class="badge bg-info text-dark">{{ txn.status }}</span>{% endif %}
                          </div>
                          <div>
                            {% if txn.status == 'pending' %}
                              <button class="btn btn-secondary btn-sm" disabled>بانتظار موافقة الإدارة</button>
                            {% else %}
                              <form method="post" action="{% url 'request_cancel_transaction' txn.doc_id %}" style="display:inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-outline-danger btn-sm">طلب إلغاء</button>
                              </form>
                            {% endif %}
                          </div>
                        </li>
                      {% endfor %}
                    </ul>
                  {% else %}
                    <div class="text-muted">لا يوجد معاملات حالية.</div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End User Reservations and Transactions Section -->
      <div class="row justify-content-center gap-4">
        <!-- Services title -->
        <div class="col-12 text-center mb-4">
          <h2 class="services-title">
            تمتع بخدمات المجلس الإلكترونية
          </h2>
          <p class="services-subtitle">إختر الخدمة التي ترغب بها</p>
          {% if status and request.method == "POST" %}
          <div class="alert text-center mt-1 mb-0" role="alert" id="alertMsg">
            {% if status == "success" %}
            <script>
              // Enable smooth scrolling for anchor links
              document
                .querySelectorAll('a.nav-link[href^="#"]')
                .forEach((anchor) => {
                  anchor.addEventListener("click", function (e) {
                    e.preventDefault();
                    document
                      .querySelector(this.getAttribute("href"))
                      .scrollIntoView({
                        behavior: "smooth",
                      });
                  });
                });
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-success text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>نجاح:</strong> تم إرسال الطلب بنجاح!
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            <!-- Please note that: the conflict slot status only for hall reservation -->
            {% elif status == "conflict slot" %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، لديك حجز بالفعل.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% elif status == "conflict transaction" %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، لديك طلب بالفعل بنفس نوع المعاملة.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            <!-- Please note that: this date is already reserved status only for hall reservation -->
            {% elif status == "this date is already reserved" %}
            <script>
              const delay = 6000; // 6 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، يوجد تعارض في تواريخ الحجز أو حجز بالفعل في نفس الفترة التي قمت بإختيارها.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 6 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% else %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-danger text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>خطأ:</strong> حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% endif %}
          </div>
          {% endif %}
        </div>
        <!-- Service buttons -->
        <div class="row g-4 justify-content-center">
          <div class="col-lg-3 col-md-4 col-sm-6">
            {% if user_debts_amount <= 240 %}
            <div class="service-card"
                 id="transactionBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#transactionsModal"
                 role="button"
                 tabindex="0"
                 aria-label="طلب معاملة">
              <div class="service-icon">
                <img src="{% static 'images/transaction_logo.png' %}" alt="طلب معاملة" />
              </div>
              <span class="service-title">طلب معاملة</span>
              <p class="service-description">قدم طلب للحصول على معاملة رسمية</p>
            </div>
            {% else %}
            <div class="service-card"
                 id="transactionBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#debtRestrictionModal"
                 role="button"
                 tabindex="0"
                 aria-label="طلب معاملة">
              <div class="service-icon">
                <img src="{% static 'images/transaction_logo.png' %}" alt="طلب معاملة" />
              </div>
              <span class="service-title">طلب معاملة</span>
              <p class="service-description">قدم طلب للحصول على معاملة رسمية</p>
            </div>
            {% endif %}
          </div>
          <div class="col-lg-3 col-md-4 col-sm-6">
            <div class="service-card"
                 id="tabooBtn"
                 role="button"
                 tabindex="0"
                 aria-label="أحواض قرية كفرعين">
              <div class="service-icon">
                <img src="{% static 'images/taboo_logo.png' %}" alt="أحواض قرية كفرعين" />
              </div>
              <span class="service-title">أحواض قرية كفرعين</span>
              <p class="service-description">عرض معلومات أحواض القرية</p>
            </div>
          </div>
          <div class="col-lg-3 col-md-4 col-sm-6">
            <div class="service-card"
                 id="hallReservationBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#hallReservationModal"
                 role="button"
                 tabindex="0"
                 aria-label="إحجز قاعة المجلس">
              <div class="service-icon">
                <img src="{% static 'images/hall_logo.png' %}" alt="إحجز قاعة المجلس" />
              </div>
              <span class="service-title">إحجز قاعة المجلس</span>
              <p class="service-description">احجز قاعة المجلس للمناسبات</p>
            </div>
          </div>
          <div class="col-lg-3 col-md-4 col-sm-6">
            {% if user_debts_amount > 240 %}
            <div class="service-card"
                 id="debtsCheckBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#debtsCheckModal"
                 role="button"
                 tabindex="0"
                 aria-label="تحقق من مستحقات النفايات">
              <div class="service-icon">
                <img src="{% static 'images/check_debts_logo.png' %}" alt="تحقق من مستحقات النفايات" />
              </div>
              <span class="service-title">تحقق من مستحقات النفايات</span>
              <p class="service-description">اطلع على مستحقات النفايات المترتبة</p>
              <div class="service-badge bg-warning">مستحقات متوفرة</div>
            </div>
            {% else %}
            <div class="service-card"
                 id="debtsCheckBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#noDebtsModal"
                 role="button"
                 tabindex="0"
                 aria-label="تحقق من مستحقات النفايات">
              <div class="service-icon">
                <img src="{% static 'images/check_debts_logo.png' %}" alt="تحقق من مستحقات النفايات" />
              </div>
              <span class="service-title">تحقق من مستحقات النفايات</span>
              <p class="service-description">اطلع على مستحقات النفايات المترتبة</p>
              <div class="service-badge bg-success">لا توجد مستحقات</div>
            </div>
            {% endif %}
          </div>
          <div class="col-lg-3 col-md-4 col-sm-6">
            <div class="service-card"
                 id="waterServiceBtn"
                 data-bs-toggle="modal"
                 data-bs-target="#waterServiceModal"
                 role="button"
                 tabindex="0"
                 aria-label="شحن عدادات المياه">
              <div class="service-icon">
                <img src="{% static 'images/water_logo.png' %}" alt="شحن عدادات المياه" />
              </div>
              <span class="service-title">شحن عدادات المياه</span>
              <p class="service-description">خدمة شحن عدادات المياه الإلكترونية</p>
              <div class="service-badge bg-primary">خدمة جديدة</div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End of Services Section -->
    <!-- Complaints and Suggestions Section -->
    <section id="complaints-suggestions">
      <div class="container px-4 my-5">
        <div class="row gx-4 justify-content-center">
          <div class="col-lg-8 complaints-box">
            <h2 class="complaints-title text-center">صندوق الشكاوى والإقتراحات</h2>
            <p class="complaints-subtitle text-center">
              نرحب بآرائكم ومقترحاتكم، فهي تعكس مدى رضاكم عن خدماتنا. نسعى
              دائماً لتحسين خدماتنا، شاركنا أفكارك واقتراحاتك لتكون جزءاً من
              التطوير والتميز.
            </p>
            <form
              method="post"
              action="{% url 'make_suggestions_complaints' %}"
            >
              {% csrf_token %}
              <div class="mb-3">
                <label for="suggestionsProviderName" class="form-label"
                  >الإسم الرباعي (إختياري)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="suggestionsProviderName"
                  name="suggestionsProviderName"
                  maxlength="40"
                  title="الإسم الرباعي"
                  oninput="this.value = this.value.replace(/[^ء-ي ]/g, '')"
                  placeholder="أدخل الإسم الرباعي باللغة العربية (إختياري)"
                />
              </div>
              <div class="mb-3">
                <!-- The allowed letters only the arabic letters and with some punctuation marks (: . ،) -->
                <label for="suggestionsMessage" class="form-label"
                  >نص الشكوى أو الإقتراح</label
                >
                <textarea
                  class="form-control"
                  id="suggestionsMessage"
                  name="suggestionsMessage"
                  rows="5"
                  placeholder="أدخل نص الشكوى أو الإقتراح هنا..."
                  maxlength="120"
                  oninput="this.value = this.value.replace(/[^ء-ي .,،:]/g, '')"
                  title="نص الشكوى أو الإقتراح يجب أن يتكون من حروف عربية فقط"
                  required
                ></textarea>
                <div class="text-end text-light">
                  <small>عدد الحروف:</small>
                  <span id="charCount">0</span>/120
                </div>
              </div>
              <button type="submit" class="btn btn-submit">
                <i class="fas fa-paper-plane me-2"></i>إرسال
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Modals -->
    <!-- Get transaction modal template -->
    {% include 'KLC_App/transaction_modal.html' %}
    <!-- Get Hall Reservation modal template -->
    {% include 'KLC_App/hall_reservation_modal.html' %}
    <!-- Get debt restriction modal template - For the users have debts more than 0-->
    {% include 'KLC_App/debt_restriction_modal.html' %}
    <!-- Get debts check modal template - For the users have debts more than 0-->
    {% include 'KLC_App/exist_debts_modal.html' %}
    <!-- Get no debts modal template - For the users have debts equal to 0-->
    {% include 'KLC_App/no_debts_modal.html' %}
    <!--Get water service modal -->
    {% include 'KLC_App/water_service_modal.html' %}
    <!-- Footer-->
    <div id="contact-us">{% include 'KLC_App/footer.html' %}</div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" aria-label="Toggle theme">
      <i class="fas fa-moon"></i>
    </button>

    <!-- Bootstrap 5.3 JS and Popper.js (needed for some components) -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
    <script>
      /// function to redirect to taboo page
      function redirectToTaboo() {
        tabooBtn.addEventListener("click", function (event) {
          event.preventDefault(); // Prevent the default action
          const delay = 4000; // 4 seconds delay
          const loadingAnimation = document.createElement("div");
          loadingAnimation.className = "loading-animation";
          loadingAnimation.innerHTML = `
                    <div class="loader"></div>
                    <p class="text-center" style="color: white; font-size: 1.5rem; margin-top: 20px;">سيتم تحويلك إلى صفحة مكتب تسوية كفرعين/هيئة تسوية الأراضي والمياه، إنتظر قليلاً.....</p>
                `;
          loadingAnimation.style.position = "fixed";
          loadingAnimation.style.top = "50%";
          loadingAnimation.style.left = "50%";
          loadingAnimation.style.transform = "translate(-50%, -50%)";
          loadingAnimation.style.zIndex = "9999";
          loadingAnimation.style.display = "flex";
          loadingAnimation.style.flexDirection = "column";
          loadingAnimation.style.justifyContent = "center";
          loadingAnimation.style.alignItems = "center";
          loadingAnimation.style.background = "rgba(0, 0, 0, 0.8)";
          loadingAnimation.style.width = "100vw";
          loadingAnimation.style.height = "100vh";

          const loader = loadingAnimation.querySelector(".loader");
          loader.style.border = "16px solid #f3f3f3";
          loader.style.borderTop = "16px solid rgb(197, 79, 0)";
          loader.style.borderRadius = "50%";
          loader.style.width = "120px";
          loader.style.height = "120px";
          loader.style.animation = "spin 2s linear infinite";

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
          document.head.appendChild(style);

          document.body.appendChild(loadingAnimation);
          setTimeout(() => {
            document.body.removeChild(loadingAnimation);
            window.open(
              "https://lwsc.ps/mob/works3.php?id=142",
              "_blank",
              "noopener,noreferrer"
            );
          }, delay);
        });
      }
      // Enhanced service card interactions
      document.addEventListener("DOMContentLoaded", function () {
        redirectToTaboo();

        // Add click handlers for service cards
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
          // Handle modal triggers
          const modalTarget = card.getAttribute('data-bs-target');
          if (modalTarget) {
            card.addEventListener('click', function() {
              const modal = new bootstrap.Modal(document.querySelector(modalTarget));
              modal.show();
            });
          }

          // Add keyboard support
          card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              card.click();
            }
          });

          // Add loading state on click
          card.addEventListener('click', function() {
            card.classList.add('loading');
            setTimeout(() => {
              card.classList.remove('loading');
            }, 1000);
          });
        });
      });
    </script>
    <!-- Script to handle the character count for the suggestions message -->
    <script>
      const suggestionsMessage = document.getElementById("suggestionsMessage");
      const charCount = document.getElementById("charCount");

      suggestionsMessage.addEventListener("input", function () {
        charCount.textContent = this.value.length;
      });
    </script>
  </body>
</html>
