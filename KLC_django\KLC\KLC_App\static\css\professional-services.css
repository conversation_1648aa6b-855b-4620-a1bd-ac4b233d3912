/* Professional Services Page Styles */

/* Main layout and background */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    padding-top: 6rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
}

/* Services section styling */
#services-section {
    position: relative;
    margin-top: 2rem !important;
    padding: 3rem 0 !important;
}

.services-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Services title styling */
.services-title {
    position: relative;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    color: #2c3e50;
    font-weight: 700;
    font-size: 2.5rem;
}

.services-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.services-subtitle {
    color: #5a6c7d;
    font-weight: 400;
    font-size: 1.2rem;
    margin-bottom: 3rem;
}

/* Professional Service Cards */
.service-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(102, 126, 234, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    position: relative;
    z-index: 1;
    margin-bottom: 1.5rem;
}

.service-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.service-card:hover .service-icon img {
    transform: scale(1.1);
}

.service-title {
    position: relative;
    z-index: 1;
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.service-description {
    position: relative;
    z-index: 1;
    color: #5a6c7d;
    font-size: 0.95rem;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Service badges */
.service-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 2;
}

.service-badge.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.service-badge.bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.service-badge.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.service-card:hover .service-badge {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Complaints and suggestions section */
#complaints-suggestions {
    margin-top: 4rem;
    margin-bottom: 3rem;
}

.complaints-box {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
}

.complaints-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    z-index: 0;
}

.complaints-title {
    position: relative;
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 700;
    font-size: 2rem;
    z-index: 1;
}

.complaints-subtitle {
    color: #5a6c7d;
    position: relative;
    z-index: 1;
    padding-bottom: 1.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Professional Form styling */
.complaints-box .form-control {
    background-color: rgba(102, 126, 234, 0.05);
    border: 2px solid rgba(102, 126, 234, 0.2);
    color: #2c3e50;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

.complaints-box .form-control:focus {
    background-color: rgba(102, 126, 234, 0.08);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
    color: #2c3e50;
    outline: none;
}

.complaints-box .form-control::placeholder {
    color: rgba(90, 108, 125, 0.7);
}

.complaints-box .form-label {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 1rem 2.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    z-index: 1;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .services-container {
        padding: 2.5rem 1.5rem;
    }
}

@media (max-width: 992px) {
    .service-card {
        padding: 1.75rem 1.25rem;
    }

    .service-icon img {
        width: 70px;
        height: 70px;
    }

    .service-title {
        font-size: 1.2rem;
    }

    .service-description {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    #services-section {
        padding: 2rem 0 !important;
    }

    .services-container {
        padding: 2rem 1rem;
    }

    .services-title {
        font-size: 2rem;
    }

    .services-subtitle {
        font-size: 1.1rem;
    }

    .complaints-box {
        padding: 2rem 1.5rem;
    }

    .complaints-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 576px) {
    .service-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem 1rem;
    }

    .service-icon img {
        width: 60px;
        height: 60px;
    }

    .service-title {
        font-size: 1.1rem;
    }

    .service-description {
        font-size: 0.85rem;
    }

    .services-title {
        font-size: 1.75rem;
    }

    .services-subtitle {
        font-size: 1rem;
    }

    .complaints-box {
        padding: 1.5rem 1rem;
    }
}

/* Dark Mode Support */
[data-theme="dark"] body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

[data-theme="dark"] .services-container {
    background: rgba(44, 62, 80, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .services-title {
    color: #ecf0f1;
}

[data-theme="dark"] .services-subtitle {
    color: rgba(236, 240, 241, 0.8);
}

[data-theme="dark"] .service-card {
    background: rgba(52, 73, 94, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .service-title {
    color: #ecf0f1;
}

[data-theme="dark"] .service-description {
    color: rgba(236, 240, 241, 0.7);
}

[data-theme="dark"] .complaints-box {
    background: rgba(44, 62, 80, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .complaints-title {
    color: #ecf0f1;
}

[data-theme="dark"] .complaints-subtitle {
    color: rgba(236, 240, 241, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .complaints-box .form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
}

[data-theme="dark"] .complaints-box .form-control:focus {
    background-color: rgba(255, 255, 255, 0.08);
    border-color: rgba(102, 126, 234, 0.5);
}

[data-theme="dark"] .complaints-box .form-control::placeholder {
    color: rgba(236, 240, 241, 0.5);
}

[data-theme="dark"] .complaints-box .form-label {
    color: #ecf0f1;
}

/* Focus and accessibility improvements */
.service-card:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

.service-card:focus-visible {
    outline: 3px solid rgba(102, 126, 234, 0.7);
}

/* Loading states */
.service-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.service-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
